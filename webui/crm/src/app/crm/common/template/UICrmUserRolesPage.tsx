import React from 'react';
import * as FeatherIcon from 'react-feather';
import { input, entity, bs, app, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { CrmUserRoleList, CrmUserRolePlugin } from './CrmUserRoleList';

const { UIEmployeeList, UIEmployeeListPlugin } = module.company.hr;


export class UICrmUserRolesPage extends app.AppComponent {
  viewId: number = util.IDTracker.next();
  uiListRef: React.RefObject<CrmUserRoleList>;

  constructor(props: app.AppComponentProps) {
    super(props);
    this.uiListRef = React.createRef<CrmUserRoleList>();
  }

  addUserRole = (type: 'SALE_AGENT' | 'SALE_FREEHAND' | 'OTHER') => {
    const { appContext, pageContext } = this.props;

    if (!this.uiListRef.current) return;

    const records = this.uiListRef.current.getVGridContext().model.getRecords();
    const excludeRecordsFilter = new entity.ExcludeRecordFilter(records, 'accountId', 'accountId');

    let onMultiSelect = (pageCtx: app.PageContext, employees: Array<any>, employeeListRef: React.RefObject<any>) => {
      if (!employees.length) {
        bs.notificationShow("warning", "No Employee Selected");
        return;
      }

      const context = employeeListRef.current.getVGridContext();
      const selectedDepartment = context.getAttribute('currentDepartment');

      if (!selectedDepartment) {
        bs.notificationShow("warning", "No department selected");
        return;
      }

      const employeeIds = employees.map(employee => employee.id);

      let params = {
        ids: employeeIds,
        department: selectedDepartment,
        type: type
      };

      appContext.createBackendCall('CRMUserRoleService', 'addCrmUserRoles', params)
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", (`Add User Roles Success`));
          pageCtx.back();
          this.forceUpdate();
        })
        .call();
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      const employeeListRef = React.createRef<any>();
      return (
        <div className='flex-vbox'>
          <UIEmployeeList ref={employeeListRef}
            appContext={appCtx} pageContext={pageCtx} readOnly={true} type={'selector'}
            plugin={new UIEmployeeListPlugin().withRecordFilter(excludeRecordsFilter)}
            onSelect={(_appCtx, pageCtx, employee) => onMultiSelect(pageCtx, [employee], employeeListRef)}
            onMultiSelect={(_appCtx, pageCtx, employees) => onMultiSelect(pageCtx, employees, employeeListRef)} />
        </div>
      )
    }
    pageContext.createPopupPage(`select-employee-${util.IDTracker.next()}`, 'Select Employees', createAppPage, { size: "xl", backdrop: "static" })
  }
  pattern: string = '';
  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.pattern = newVal;
      if (this.uiListRef.current) {
        let uiList: CrmUserRoleList = this.uiListRef.current;
        uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
        uiList.getVGridContext().model.filter();
        uiList.forceUpdate();
      }
    }
  }

  syncUserRoles = () => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('CRMUserRoleService', 'syncCrmUserRoles',)
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Successfully synchronized ${data} user roles`);
        this.uiListRef.current?.reloadData();
      })
      .call();
  }

  renderHeader() {
    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Trello className="me-2" size={18} />
            CRM Users
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
            <input.WStringInput className={'flex-hbox'} style={{ maxWidth: 300 }}
              name='search' value={this.pattern}
              placeholder={('Enter Network or Event...')} onChange={this.onChangePattern} />
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >
          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.syncUserRoles}>
            <FeatherIcon.RefreshCcw size={14} className="me-2" />
            Sync From HRM
          </bs.Button>

          <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" offset={[0, 5]}>
            <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
              <FeatherIcon.Plus size={14} className="me-1" />
              Add
              <FeatherIcon.ChevronDown size={14} className="ms-1" />
            </bs.PopoverToggle>
            <bs.PopoverContent>
              <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>
                <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
                  onClick={() => this.addUserRole('SALE_AGENT')}>
                  <FeatherIcon.User size={14} className="me-1" />
                  SALE AGENT
                </bs.Button>
                <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
                  onClick={() => this.addUserRole('SALE_FREEHAND')}>
                  <FeatherIcon.User size={14} className="me-1" />
                  SALE FREEHAND
                </bs.Button>
                <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
                  onClick={() => this.addUserRole('OTHER')}>
                  <FeatherIcon.User size={14} className="me-1" />
                  OTHER
                </bs.Button>
              </div>
            </bs.PopoverContent>
          </bs.Popover>
        </div>
      </div>
    )
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;
    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>
          <CrmUserRoleList ref={this.uiListRef}
            appContext={appContext} pageContext={pageContext} plugin={new CrmUserRolePlugin()} />
        </div>
      </div>
    )
  }
}
