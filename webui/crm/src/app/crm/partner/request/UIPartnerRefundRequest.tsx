
import React from 'react';
import * as FeatherIcon from "react-feather";
import { util, bs, input, entity, app } from "@datatp-ui/lib";
import { module } from '@datatp-ui/erp';

import { T } from 'app/crm/sales';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';

import BBRefCountry = module.settings.BBRefCountry;
import { SOURCE_OPTIONS, UIPartnerRequestApproval } from './UIPartnerRequest';

type RequestStatus = 'NEW' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
type PartnerGroup = 'CUSTOMERS' | 'COLOADERS' | 'AGENTS';

const SESSION = app.host.DATATP_HOST.session;

function validatePartner(partner: any) {
  let agentCategories: string[] = ['AGENT_OVERSEAS', 'AGENT_DOMESTIC']

  if (agentCategories.includes(partner['category'])) return;
  let missingFields: string[] = [];

  if (!partner['label'] || !partner['localizedLabel']) missingFields.push('Partner Name (En/Vn)');
  if (!partner['address'] || !partner['localizedAddress']) missingFields.push('Address (En/Vn)');
  if (partner['category'] != 'AGENT_DOMESTIC' && partner['category'] != 'AGENT_OVERSEAS' && !partner['refund'] && !partner['provinceId']) missingFields.push('Province');
  if (!partner['countryId']) missingFields.push('Country');
  if (!partner['personalContact']) missingFields.push('Personal Contact');
  if (!partner['cell']) missingFields.push('Cell Phone');
  if (!partner['email']) missingFields.push('Email');
  if (!partner['industryCode'] || !partner['industryLabel']) missingFields.push('Industry');

  if (missingFields.length > 0) {
    bs.dialogShow('Missing Information',
      <div className="text-danger fw-bold text-center py-3 border-bottom">
        <FeatherIcon.AlertCircle className="mx-2" />
        {`Please provide: ${missingFields.join(', ')}.`}
      </div>,
      { backdrop: 'static', size: 'sm' }
    );
    throw new Error(`Please provide: ${missingFields.join(', ')}.`);
  }
}

interface UIPartnerRefundRequestEditorProps extends entity.AppDbEntityEditorProps {
  request: any;
  space: 'User' | 'Company' | 'System';
}
export class UIPartnerRefundRequestEditor extends entity.AppDbEntityEditor<UIPartnerRefundRequestEditorProps> {

  source: { code: string, label: string }[] = [];

  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('CRMPartnerService', 'findByTaxCode', { taxCode: newVal })
      .withSuccessData((partnerList: any[]) => {
        if (partnerList.length > 0) {
          let message = (
            <div className="ms-1 text-warning py-3 border-bottom">
              A Partner with the tax code "{newVal}" already exists in the system.
            </div>
          );
          bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'sm' });
        }
      })
      .call();
  }

  onUpdateSimilarFields = (bean: any, field: string, _oldVal: any, newVal: any) => {
    bean[field] = newVal;
    if (field === 'name') {
      if (!bean['label'] || bean['label'].length == 0) bean['label'] = newVal;
      if (!bean['localizedLabel'] || bean['localizedLabel'].length == 0) bean['localizedLabel'] = newVal;
    }
    if (field === 'address') {
      if (!bean['localizedAddress'] || bean['localizedAddress'].length == 0) bean['localizedAddress'] = newVal;
    }
    this.forceUpdate();
  }

  onPreCommit = (observer: entity.BeanObserver) => {
    let partner = observer.getMutableBean();
    validatePartner(partner);
    partner['source'] = this.source.filter(sel => !!sel.label).map(sel => sel.label).join(';');
  }

  onPostUpdateSource = (_inputUI: React.Component, bean: any[], selectOpt: any, _userInput: string) => {
    if (!selectOpt || Object.keys(selectOpt).length === 0 || !selectOpt['label']) return
    let label: string = selectOpt['label']
    let filtered: any[] = (bean || []).filter(sel => sel['label'] !== label);
    let newBean: any = {
      code: '',
      label: selectOpt['label'],
    };
    filtered.push(newBean);
    this.source = filtered;
    this.nextViewId();
    this.forceUpdate();
  }

  onChangeStatus = (newStatus: RequestStatus) => {
    let { appContext, pageContext, observer, request, onPostCommit } = this.props;
    this.onPreCommit(observer);
    let partner = observer.getMutableBean();

    if (newStatus == 'APPROVED' || newStatus == 'REJECTED') {
      appContext
        .createHttpBackendCall('CRMPartnerService', 'saveCRMPartner', { entity: partner })
        .withSuccessData((data: any) => {
          pageContext.back();
          this.onPostCommit(data);

          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            const _onPostCommit = (entity: any, _uiEditor?: bs.BaseComponent) => {
              pageCtx.back();
              if (onPostCommit) onPostCommit(entity);
            }

            request = {
              ...request,
              status: newStatus,
              approvedByAccountId: SESSION.getAccountId(),
              approvedByLabel: SESSION.getAccountAcl().getFullName(),
              approvedDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
              approvedNote: ''
            }
            return (
              <div style={{ minHeight: '100px', maxHeight: '400px' }}>
                <UIPartnerRequestApproval appContext={appCtx} pageContext={pageCtx}
                  observer={new entity.BeanObserver(request)} newStatus={newStatus} onPostCommit={_onPostCommit} />
              </div>
            );
          }
          let title = `Approve Partner: ${partner['name']}`;
          if (newStatus == 'REJECTED') title = `Reject Partner: ${partner['name']}`;
          pageContext.createPopupPage(`approval-partner-${util.IDTracker.next()}`, title, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        })
        .call();
    } else if (newStatus == 'NEW' || newStatus == 'CANCELLED') {
      let { appContext, request, onPostCommit } = this.props;
      request['status'] = newStatus;
      appContext
        .createHttpBackendCall('PartnerRequestService', 'updatePartnerRequestStatus', { request: request })
        .withSuccessData((data: any) => {
          appContext.addOSNotification("success", T('Success'));
          pageContext.back();
          if (onPostCommit) onPostCommit(data);
        })
        .call();
    }
  }

  render() {
    let { appContext, pageContext, observer, space, readOnly } = this.props;
    let partner = observer.getMutableBean();
    console.log(partner);

    let status: RequestStatus = partner['status'] || 'NEW';
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly
    let partnerGroup: PartnerGroup = partner['partnerGroup'] ? partner['partnerGroup'] : 'CUSTOMERS';
    let categories = [];
    if (partnerGroup === 'CUSTOMERS') {
      categories = ['CUSTOMER', 'COLOADER', 'SHIPPER', 'CONSIGNEE']
    } else if (partnerGroup === 'AGENTS') {
      categories = ['AGENT_OVERSEAS', 'AGENT_DOMESTIC']
    } else if (partnerGroup === 'COLOADERS') {
      categories = ['TRUCKER', 'CARRIER', 'AIRLINE', 'FORWARDER', 'OTHER']
    } else {
      categories = ['OTHER']
    }

    if ((!this.source || this.source.length < 1) && partner['source']) {
      let sources: any[] = partner['source'] ? partner['source'].split(';') : [];
      this.source = sources.filter(sel => !!sel).map(sel => { return { code: sel, label: sel } });
    }

    return (
      <div className="flex-vbox">
        <div className='flex-vbox shadow-sm rounded h-100 bg-white'>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='partnerCode' label={T("Partner No.")} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField
                bean={partner} field='category' label={T("Category.")} disable={!writeCap || !observer.isNewBean()}
                options={categories} />
            </bs.Col>
            <bs.Col span={6}>
              <BBRefCrmUserRole minWidth={500} hideMoreInfo label='Saleman'
                appContext={appContext} pageContext={pageContext} bean={partner} disable={!writeCap}
                beanIdField='requestSalemanAccountId' beanLabelField='requestSalemanLabel' placeholder='Select Saleman Request...'
                onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                  bean['requestSalemanAccountId'] = selectOpt['accountId'];
                  bean['requestSalemanLabel'] = selectOpt['fullName'];
                  this.forceUpdate();
                }} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={12}>
              <input.BBStringField
                bean={partner} field='name' label={T("Partner Name (Abb)")} disable={!writeCap} required
                onInputChange={this.onUpdateSimilarFields} />
            </bs.Col>
            <bs.Col span={12}>
              <input.BBStringField
                bean={partner} field='label' label={T("Partner Name (En)")} disable={!writeCap} />
            </bs.Col>

            <bs.Col span={12}>
              <input.BBStringField
                bean={partner} field='localizedLabel' label={T("Partner Name (VN)")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            {
              partnerGroup === 'CUSTOMERS' ?
                <>
                  <bs.Col span={3}>
                    <input.BBStringField bean={partner} label={T('Investment Origin')} field="investmentOrigin" disable={!writeCap} />
                  </bs.Col>
                  <bs.Col span={3}>
                    <BBRefCountry key={partner.kcnCode}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                      required bean={partner} beanIdField={'countryId'} hideMoreInfo
                      beanLabelField={'countryLabel'} refCountryBy='id'
                      onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                        bean['countryId'] = selectOpt['id'];
                        bean['countryLabel'] = selectOpt['label'];
                        this.forceUpdate();
                      }} />
                  </bs.Col>
                  <bs.Col span={6}>
                    <input.BBSelectField bean={partner} field="source" label={'Source'}
                      options={SOURCE_OPTIONS} />
                  </bs.Col>
                </>
                :
                <>
                  <bs.Col span={6}>
                    <BBRefCountry key={partner.kcnCode}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                      required bean={partner} beanIdField={'countryId'} hideMoreInfo
                      beanLabelField={'countryLabel'} refCountryBy='id'
                      onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                        bean['countryId'] = selectOpt['id'];
                        bean['countryLabel'] = selectOpt['label'];
                        this.forceUpdate();
                      }} />
                  </bs.Col>

                  <bs.Col span={6}>
                    <module.resource.BBRefMultiResource
                      key={`new-partner-network-${this.viewId}`}
                      appContext={appContext} pageContext={pageContext}
                      placement="bottom-start" offset={[0, 5]} minWidth={350}
                      disable={!writeCap} label={T('Source')} placeholder="Enter Source"
                      bean={this.source} beanIdField={'code'} hideMoreInfo
                      beanLabelField={'label'} resourceType={"network"} refResourceBy="identifier"
                      onPostUpdate={this.onPostUpdateSource} />
                  </bs.Col>
                </>
            }
          </bs.Row>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBTextField
                bean={partner} label={T('Address (En)')} field="address" disable={!writeCap} required
                onInputChange={this.onUpdateSimilarFields} style={{ height: '4em' }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBTextField
                bean={partner} label={T('Address (Vn)')} field="localizedAddress" disable={!writeCap} required
                style={{ height: '4em' }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='fax' label={T("FAX.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='email' label={T("Email")} disable={!writeCap} required validators={[util.validator.EMAIL_VALIDATOR]} />
            </bs.Col>
          </bs.Row>

          <bs.Row>


            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='swiftCode' label={T("Swift Code.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bankName' label={T("Bank Name.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bankAccount' label={T("Bank Account.")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bankAddress' label={T("Bank Address.")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          {
            partnerGroup === 'AGENTS' ?
              <bs.Row>
                <bs.Col span={6}>
                  <module.resource.BBRefResource
                    key={`new-refund-partner-industry-${this.viewId}`}
                    appContext={appContext} pageContext={pageContext}
                    placement="bottom-start" offset={[0, 5]} minWidth={350}
                    disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                    required bean={partner} beanIdField={'industryCode'} hideMoreInfo
                    beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBStringField
                    bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Enter Route'
                  />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBSelectField
                    bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                    options={['Domestic', 'Overseas']}
                  />
                </bs.Col>
              </bs.Row>
              :
              <bs.Row>
                <bs.Col span={3}>
                  <input.BBSelectField bean={partner} field="groupName" label={T('Partner Group Type')}
                    options={['NORMAL', 'FACTORY', 'CO-LOADER', 'OTHERS']} disable={!writeCap} />
                </bs.Col>

                <bs.Col span={3}>
                  <module.resource.BBRefResource
                    key={`new-refund-partner-industry-${this.viewId}`}
                    appContext={appContext} pageContext={pageContext}
                    placement="bottom-start" offset={[0, 5]} minWidth={350}
                    disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                    required bean={partner} beanIdField={'industryCode'} hideMoreInfo
                    beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBStringField
                    bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Enter Route'
                  />
                </bs.Col>
                <bs.Col span={3}>
                  <input.BBSelectField
                    bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                    options={['Domestic', 'Overseas']}
                  />
                </bs.Col>
              </bs.Row>
          }


          <input.BBTextField
            bean={partner} label={T('Note')} field="note" disable={!writeCap}
            style={{ height: '8em', fontSize: '1rem' }} />

        </div>

        <bs.Toolbar className='border' hide={readOnly}>

          <bs.Button laf="success" className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={space !== 'System' || !['NEW', 'PENDING'].includes(status)}
            onClick={() => this.onChangeStatus('APPROVED')}>
            <FeatherIcon.CheckCircle size={12} /> Approve
          </bs.Button>

          <bs.Button laf="danger" className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={space !== 'System' || !['NEW', 'PENDING'].includes(status)}
            onClick={() => this.onChangeStatus('REJECTED')}>
            <FeatherIcon.XCircle size={12} /> Reject
          </bs.Button>

          <bs.Button laf="primary" className="px-2 py-1 mx-1" style={{ width: 160 }}
            hidden={space !== 'User' || !['CANCELLED', 'REJECTED'].includes(status)}
            onClick={() => this.onChangeStatus('NEW')}>
            <FeatherIcon.Mail size={12} /> Resend Request
          </bs.Button>

          <bs.Button laf="danger" className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={space !== 'User' || !['NEW', 'PENDING', 'REJECTED'].includes(status)}
            onClick={() => this.onChangeStatus('CANCELLED')}>
            <FeatherIcon.XCircle size={12} /> Cancel
          </bs.Button>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: 'Customer', context: 'company',
              service: "CRMPartnerService", commitMethod: "saveCRMPartner"
            }}
            onPreCommit={this.onPreCommit} onPostCommit={this.onPostCommit} />

        </bs.Toolbar>
      </div >
    )
  }
}