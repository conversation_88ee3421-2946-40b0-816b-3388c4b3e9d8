package cloud.datatp.fforwarder.sales.integration;

import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("BFSOneCRMService")
public class BFSOneCRMService extends BaseComponent {

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  @Transactional
  public List<SqlMapRecord> findBookingLocalByHawb(ClientContext client, String hawb) {
    return bfsOneCRMLogic.findBookingLocalByHawb(client, hawb);
  }

  /* ------------------ B<PERSON>One Partner ----------------------------*/
  @Transactional
  public CRMPartner createBFSOnePartner(ClientContext client, CRMPartner partner) {
    return bfsOneCRMLogic.createBFSPartner(client, partner);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> checkPartnerExistInSystem(ClientContext client, String searchPattern) {
    return bfsOneCRMLogic.checkPartnerExistInSystem(client, searchPattern);
  }

  @Transactional(readOnly = true)
  public MapObject loadLead(ClientContext client, String leadCode, Employee employee) {
    return bfsOneCRMLogic.loadLead(client, leadCode, employee);
  }
}