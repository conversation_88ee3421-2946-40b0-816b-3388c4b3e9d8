package cloud.datatp.fforwarder.core.partner.entity;

import java.util.Date;
import java.util.HashSet;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fforwarder.core.bd.AnnualConferenceMessagePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = PartnerRequest.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = PartnerRequest.TABLE_NAME + "_partner_id",
      columnNames = {"partner_id"}
    ),
  },
  indexes = {
    @Index(
      name = PartnerRequest.TABLE_NAME + "_partner_id_idx",
      columnList = "partner_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_request_by_account_idx",
      columnList = "request_by_account_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_approved_by_account_idx",
      columnList = "approved_by_account_id"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class PartnerRequest extends PersistableEntity<Long> {

  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_forwarder_partner_request";

  public enum PartnerRequestStatus {
    NEW, PENDING, APPROVED, REJECTED, CANCELLED;

    public static PartnerRequestStatus parse(String token) {
      if (token == null)
        return PENDING;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return PENDING;
      }
    }

    public static boolean isCompleted(PartnerRequestStatus status) {
      return status == PartnerRequestStatus.APPROVED || status == PartnerRequestStatus.REJECTED
        || status == PartnerRequestStatus.CANCELLED;
    }
  }

  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "bfsone_partner_code_temp")
  private String bfsonePartnerCodeTemp;

  @Column(name = "partner_name")
  private String partnerName;

  @Column(name = "partner_label")
  private String partnerLabel;

  @Enumerated(EnumType.STRING)
  private PartnerRequestStatus status = PartnerRequestStatus.NEW;

  @Column(name = "request_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date requestDate;

  @Column(name = "request_by_account_id")
  private Long requestByAccountId;

  @Column(name = "request_by_label")
  private String requestByLabel;

  // from setup
  @Column(name = "approved_by_account_id")
  private Long approvedByAccountId;

  @Column(name = "approved_by_label")
  private String approvedByLabel;

  @Column(name = "approved_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date approvedDate;

  @Column(name = "approved_note", length = 1024 * 4)
  private String approvedNote;

  // from setup
  @Column(name = "mail_to")
  private String to;

  // from setup
  @Column(name = "mail_cc")
  private String cc;

  public PartnerRequest(CRMPartner partner) {
    this.status = PartnerRequestStatus.NEW;
    this.requestDate = new Date();
    withCRMPartner(partner);
  }

  public PartnerRequest withCRMPartner(CRMPartner partner) {
    this.partnerId = partner.getId();
    this.partnerName = partner.getName();
    this.partnerLabel = partner.getLabel();
    this.bfsonePartnerCodeTemp = partner.getPartnerCodeTemp();
    return this;
  }

  public PartnerRequest withRequestBy(Long accountId, String label) {
    this.requestByAccountId = accountId;
    this.requestByLabel = label;
    return this;
  }

  public CRMMessageSystem toApprovalMailMessage(ClientContext client) {
    Objects.assertTrue(status == PartnerRequestStatus.APPROVED || status == PartnerRequestStatus.REJECTED, "New Status must be APPROVED or REJECTED");
    CRMMessageSystem msg = new CRMMessageSystem();
    System.out.println(buildApprovalEmailContent());
    msg.setContent(buildApprovalEmailContent());
    msg.setScheduledAt(new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AnnualConferenceMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(new HashSet<>(Arrays.asList("<EMAIL>")));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", "CRM - Notification Message");
    metadata.put("to", Arrays.asList("<EMAIL>"));
    metadata.put("ccList", Arrays.asList("<EMAIL>"));
    msg.setMetadata(metadata);

    return msg;
  }
  
  private String buildApprovalEmailContent() {
    boolean isApproved = this.status == PartnerRequestStatus.APPROVED;
    String normalizedStatus = isApproved ? "Approved" : "Rejected";
    
    String titleColor   = isApproved ? "#1f2937" : "#b91c1c";
    String badgeColor   = isApproved ? "#1f2937" : "#b91c1c";
    String panelBg      = isApproved ? "#f8fafc" : "#fef2f2";
    String panelBorder  = isApproved ? "#3b82f6" : "#ef4444";
    String sectionTitle = isApproved ? "#1f2937" : "#b91c1c";
    
    String partnerID = "";
    if (isApproved) {
      partnerID = String.format("""
          <div style="margin-bottom:12px;">
            <strong style="color:%s;">Partner ID:</strong>
            <span style="color:#374151;">%s</span>
          </div>
          """,
          sectionTitle, bfsonePartnerCodeTemp
       );
    }
    
    String approvedByLabel   = isApproved ? "Approved By"  : "Reviewed By";
    String approvedDateLabel = isApproved ? "Approved Date": "Reviewed Date";
    String noteLabel         = isApproved ? "Note": "Rejection Reason";
    
    return String.format("""
        <div style="font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif;
                    max-width:600px;margin:0 auto;padding:20px;background-color:#f9fafb;">
          <div style="background-color:#ffffff;padding:30px;border-radius:12px;
                      box-shadow:0 4px 6px rgba(0,0,0,0.1);">
            <h1 style="color:%s;font-size:20px;margin:0 0 20px 0;text-align:center;">
              Partner Creation Request for <strong>%s</strong> – 
              <strong style="color:%s;">%s</strong>
            </h1>
        
            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;">
              Dear %s,
            </p>
            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;line-height:1.6;">
              We would like to inform you that the partner creation request for 
              <strong>%s</strong> has been <strong>%s</strong>.
            </p>
        
            <div style="background-color:%s;padding:20px;border-radius:8px;margin:20px 0;
                        border-left:4px solid %s;">
              <h3 style="color:%s;margin:0 0 15px 0;font-size:18px;">Request Details</h3>
        
              %s
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Partner Name:</strong>
                <span style="color:#374151;">%s</span>
              </div>
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Requested By:</strong>
                <span style="color:#374151;">%s</span>
              </div>
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Request Date:</strong>
                <span style="color:#374151;">%s</span>
              </div>
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>
        
              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>
            </div>
        
            <div style="margin-top: 30px; text-align: center;">
                <p style="color: #374151; margin: 0;">
                    Best regards,<br>
                    <strong>CRM Management Team</strong>
                </p>
            </div>
        
            <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                <p style="color: #6b7280; font-size: 14px; margin: 0;">
                    This is an automated notification from the CRM Task Management System.
                </p>
            </div>
          </div>
        </div>
        """,
        // header
        titleColor, partnerName, badgeColor, normalizedStatus,
        requestByLabel,
        partnerName, normalizedStatus,
        
        // panel
        panelBg, panelBorder, sectionTitle,
        
        // details
        partnerID,
        
        sectionTitle, partnerName,
        sectionTitle, StringUtil.isNotEmpty(requestByLabel) ? requestByLabel : "N/A",
        sectionTitle, requestDate != null ? DateUtil.asCompactDate(requestDate) : "N/A",
        sectionTitle, approvedByLabel, StringUtil.isNotEmpty(this.approvedByLabel) ? this.approvedByLabel : "N/A",
        sectionTitle, approvedDateLabel, approvedDate != null ? DateUtil.asCompactDate(approvedDate) : "TBD",
        sectionTitle, noteLabel, StringUtil.isNotEmpty(approvedNote) ? approvedNote : "N/A",
            
        // signature
        "CRM Management Team"
    );
  }
}