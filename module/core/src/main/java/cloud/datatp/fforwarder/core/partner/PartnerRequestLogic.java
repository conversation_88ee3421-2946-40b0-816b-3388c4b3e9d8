package cloud.datatp.fforwarder.core.partner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest.PartnerRequestStatus;
import cloud.datatp.fforwarder.core.partner.repository.PartnerRequestRepository;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;

@Slf4j
@Component
public class PartnerRequestLogic extends CRMDaoService {

  @Autowired
  private PartnerRequestRepository requestRepo;
  
  @Autowired
  private BFSOneApi bfsOneApi;
  
  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;
  
  @Autowired
  private CRMPartnerLogic partnerLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;
  
  public PartnerRequest getById(ClientContext client, Long id) {
    return requestRepo.findById(id).get();
  }

  public List<PartnerRequest> findByPartnerId(ClientContext client, Long partnerId) {
    return requestRepo.findByPartnerId(partnerId);
  }

  public PartnerRequest savePartnerRequest(ClientContext client, PartnerRequest request) {
    request.set(client);
    return requestRepo.save(request);
  }
  
  public PartnerRequest updatePartnerRequestStatus(ClientContext client, PartnerRequest request) {
    PartnerRequest requestInDb = getById(client, request.getId());
    Objects.assertNotNull(requestInDb, "PartnerRequest is not found by id = {}", request.getId());
    
    requestInDb.setStatus(request.getStatus());
    requestInDb.setApprovedByAccountId(request.getApprovedByAccountId());
    requestInDb.setApprovedByLabel(request.getApprovedByLabel());
    requestInDb.setApprovedDate(request.getApprovedDate());
    requestInDb.setApprovedNote(request.getApprovedNote());
    
    CRMPartner partner = partnerLogic.getById(client, request.getPartnerId());
    Objects.assertNotNull(partner, "CRMPartner is not found by id = {}", request.getPartnerId());
    PartnerRequestStatus newStatus = request.getStatus();
    if (newStatus == PartnerRequestStatus.APPROVED) {
      try {
        Long approvedByAccountId = request.getApprovedByAccountId();
        if (approvedByAccountId == null) approvedByAccountId = client.getAccountId();
        CrmUserRole approvedBy = crmUserRoleLogic.getByAccountId(client, approvedByAccountId);
        Objects.assertNotNull(approvedBy, "CrmUserRole is not found by accountId = {}", approvedByAccountId);
        String approvedByBfsoneEmployeeCode = approvedBy.getBfsoneCode();
        String approvedByBfsoneUsername = approvedBy.getBfsoneUsername();
        String authenticate = bfsOneApi.authenticate(approvedByBfsoneEmployeeCode, approvedByBfsoneUsername);

        Long salemanAccountId = requestInDb.getRequestByAccountId();
        Objects.assertNotNull(salemanAccountId, "RequestedByAccountId must not be null!");
        CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
        Objects.assertNotNull(saleman, "Saleman is not found, account id = " + salemanAccountId);
        String salemanBfsoneEmployeeCode = saleman.getBfsoneCode();
        String salemanBfsoneUsername = saleman.getBfsoneUsername();
        
        MapObject bfsOnePartner = partner.toBFSOnePartner();
        bfsOnePartner.put("RequestUser", salemanBfsoneUsername);
        bfsOnePartner.put("SalemanID", salemanBfsoneEmployeeCode);
        bfsOnePartner.put("Email_Request", saleman.getEmail());
        bfsOnePartner.put("isUnApproved", null);
        bfsOnePartner.put("ReasonUnApproved", requestInDb.getApprovedNote());
        log.info("------------------------Partner---------------------------\n");
        DataSerializer.JSON.dump(bfsOnePartner);
        log.info("--------------------------------------------------------\n");

        BFSOnePartnerGroup group = partner.getPartnerGroup();
        MapObject bfsOnePartnerApproved = bfsOneApi.partnerApprove(group, authenticate, bfsOnePartner);
        String partnerCode = bfsOnePartnerApproved.getString("PartnerID", "");
        partner.setPartnerCode(partnerCode);
        partner.setPartnerCodeTemp(null);
        
        partner.setStatus(requestInDb.getStatus());
        partner = partnerLogic.save(client, partner);
        
        requestInDb.withCRMPartner(partner);
        requestInDb = savePartnerRequest(client, requestInDb);
        
        CRMMessageSystem crmMessageSystem = requestInDb.toApprovalMailMessage(client);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      } catch (Exception ex) {
        log.error("Error when create BFSOne Partner", ex);
      }
    } else {
      partner.setStatus(requestInDb.getStatus());
      partner = partnerLogic.save(client, partner);
      
      requestInDb.withCRMPartner(partner);
      requestInDb = savePartnerRequest(client, requestInDb);
      
      if (newStatus == PartnerRequestStatus.REJECTED) {
        CRMMessageSystem crmMessageSystem = requestInDb.toApprovalMailMessage(client);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      }
    }
    
    return requestInDb;
  }
  
  public List<SqlMapRecord> searchPartnerRequests(ClientContext client, SqlQueryParams sqlParams) {
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/PartnerRequestSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerRequest", sqlParams);
  }

}